# Insurance Business Document Push System Requirements Specification

## 1. Project Overview

### 1.1 Project Background
The Insurance Business Document Push System is a Web API service developed based on ASP.NET Core 3.1, primarily designed to automatically push insurance business-related documents to the A8 collaborative office system, enabling digital management of business processes.

### 1.2 Project Objectives
- Implement automated pushing of insurance business documents
- Seamlessly integrate with A8 collaborative office system
- Support document processing for multiple business types
- Provide reliable file upload and workflow triggering mechanisms

### 1.3 System Scope
The system covers core insurance business scenarios including policy management, claims processing, cover notes, and various invoice processing.

## 2. Functional Requirements

### 2.1 Core Business Functions

#### 2.1.1 Policy Document Push (Policy)
- **Function Description**: Handle pushing of policy-related documents
- **Data Source**: polh table (policy master table)
- **Trigger Condition**: fconfirm in (2,3) and fctldel = ''
- **Push Content**:
  - Process ID (fctlid)
  - Policy Number (fpolno)
  - Endorsement Number (fendtno)
  - Project Name (fpartr)
  - Contract Amount (fsum)
  - Insurance Period (fefffr - feffto)
  - Policy Summary (fcontrt_t1)
  - Policyholder (finsd)
  - Attachment Documents

#### 2.1.2 Business Report Push (Business)
- **Function Description**: Handle pushing of business reports
- **Data Source**: reportrecord table
- **Trigger Condition**: flowid = '' and status = '1'
- **Push Content**:
  - Report Name (reportname)
  - Date (reportdate)
  - Process ID (ID)
  - Attachment Documents

#### 2.1.3 Claims Report Push (Claims)
- **Function Description**: Handle pushing of claims reports
- **Data Source**: reportrecord table
- **Trigger Condition**: flowid = '' and status = '2'
- **Push Content**:
  - Report Name (reportname)
  - Date (reportdate)
  - Process ID (ID)
  - Attachment Documents

#### 2.1.4 Cover Note Push (CoverNote)
- **Function Description**: Handle pushing of cover note documents
- **Data Source**: cover_note table
- **Trigger Condition**: flowid = '' and fconfirm = '3'
- **Push Content**:
  - Cover Note Number (CoverNo)
  - Begin Date (BeginDate)
  - Insured Name (IssueName)
  - Attachment Documents

#### 2.1.5 Invoice Processing Push
Support processing of multiple invoice types:

**a) General Invoice (OinvhDRCR)**
- Data Source: oinvh table
- Trigger Condition: fstatus = '3'

**b) COIS Invoice (OinvhDRCR_COIS)**
- Data Source: oinvh table (live_isdata database)
- Trigger Condition: fposted = '1'

**c) Accounting Invoice (OinvhDRCR_Acc)**
- Data Source: oginvh table
- Trigger Condition: fposted = '1'

**d) Original Invoice (oriinvh)**
- Process original invoice data

**e) Payment Invoice (opyinvh, opyrinvh)**
- Support claims payment related invoice processing

### 2.2 File Management Functions

#### 2.2.1 File Upload
- Support PDF format document upload
- Support main documents and additional documents (RI reinsurance documents)
- Automatic file naming rules
- File size and format validation

#### 2.2.2 Attachment Management
- Integration with A8 system attachment services
- Support batch upload of multiple files
- File association to workflow instances

### 2.3 Workflow Integration

#### 2.3.1 A8 System Integration
- SSO single sign-on authentication
- Automatic workflow triggering
- Process status tracking
- Error handling and retry mechanisms

#### 2.3.2 Token Management
- SSO encrypted token acquisition
- System access token management
- REST API token processing
- Automatic token refresh

## 3. Non-Functional Requirements

### 3.1 Performance Requirements
- API Response Time: < 5 seconds
- File Upload Support: Maximum 50MB
- Concurrent Processing: Support 100 concurrent requests
- Database Connection Timeout: 400 seconds

### 3.2 Reliability Requirements
- System Availability: 99.5%
- Data consistency guarantee
- Exception handling and error recovery
- Logging and monitoring

### 3.3 Security Requirements
- API access control
- Data transmission encryption
- Sensitive information protection
- Audit log recording

### 3.4 Maintainability Requirements
- Modular design
- Configuration externalization
- Detailed error logging
- Complete API documentation

## 4. Data Structure Description

### 4.1 Core Data Models

#### 4.1.1 Policy Model
```csharp
public class Policy
{
    public string Fctlid { get; set; }      // Process ID
    public string Fpolno { get; set; }      // Policy Number
    public string Fendtno { get; set; }     // Endorsement Number
    public string Fsum { get; set; }        // Insurance Amount
    public string Fefffr { get; set; }      // Effective From
    public string Feffto { get; set; }      // Effective To
    public string Fpartr { get; set; }      // Project Name
    public string Finsd { get; set; }       // Insured
    public string Fcontrt_t1 { get; set; }  // Policy Summary
}
```

#### 4.1.2 CoverNote Model
```csharp
public class CoverNote
{
    public string Fctlid { get; set; }      // Process ID
    public string CoverNo { get; set; }     // Cover Note Number
    public string IssueName { get; set; }   // Insured Name
    public string BeginDate { get; set; }   // Begin Date
    public string IssueDate { get; set; }   // Issue Date
}
```

#### 4.1.3 DrCr Model (Invoice)
```csharp
public class DrCr
{
    public string Fctlid { get; set; }      // Process ID
    public string Finvno { get; set; }      // Invoice Number
    public string Fpolno { get; set; }      // Policy Number
    public string Fclmno { get; set; }      // Claim Number
    public string Fbilldate { get; set; }   // Bill Date
    public string Fcremark { get; set; }    // Remarks
    public string Fcinpname { get; set; }   // Handler Name
}
```

#### 4.1.4 Report Model
```csharp
public class Report
{
    public string ID { get; set; }          // Report ID
    public string reportdate { get; set; }  // Report Date
    public string reportname { get; set; }  // Report Name
    public string filename { get; set; }    // File Name
}
```

### 4.2 Data Transfer Objects

#### 4.2.1 UploadRequest
```csharp
public class UploadRequest
{
    public string Fctlid { get; set; }           // Process ID
    public string Polno { get; set; }            // Policy Number
    public string UserId { get; set; }           // User ID
    public string Postype { get; set; }          // Business Type
    public string Fpayee { get; set; }           // Payee
    public string Yccamount { get; set; }        // HKD Amount (Words)
    public string Yamount { get; set; }          // HKD Amount
    public byte[] FileContent { get; set; }      // Main Document Content
    public byte[] ExtraFileContent { get; set; } // Extra Document Content
}
```

#### 4.2.2 JsonOutput
```csharp
public class JsonOutput
{
    public string Code { get; set; }        // Response Code
    public string Message { get; set; }     // Response Message
    public string Result { get; set; }      // Response Result
}
```

### 4.3 Database Table Structure

#### 4.3.1 Main Business Tables
- **polh**: Policy master table
- **oinvh**: Invoice table
- **oginvh**: Accounting invoice table
- **cover_note**: Cover note table
- **reportrecord**: Report record table

#### 4.3.2 Database Connections
- **live_ilodata**: Main business database
- **live_isdata**: COIS system database

## 5. API Specification Overview

### 5.1 API Endpoints

#### 5.1.1 GET /api/Push
- **Function**: Query process status and update
- **Parameters**: Id (User ID)
- **Returns**: JsonOutput

#### 5.1.2 POST /api/Push
- **Function**: Document upload and push
- **Parameters**: UploadRequest object
- **Returns**: JsonOutput

### 5.2 Response Code Definitions
- **10200**: Success
- **10500**: Already uploaded
- **00000**: Operation failed

### 5.3 Business Types (Postype)
- **Policy**: Policy documents
- **Business**: Business reports
- **Claims**: Claims reports
- **CoverNote**: Cover notes
- **OinvhDRCR**: General invoices
- **OinvhDRCR_COIS**: COIS invoices
- **OinvhDRCR_Acc**: Accounting invoices
- **oriinvh**: Original invoices
- **opyinvh**: Payment invoices
- **opyrinvh**: Payment refund invoices

## 6. Technical Architecture Description

### 6.1 System Architecture
- **Architecture Pattern**: Layered architecture
- **API Layer**: ASP.NET Core Web API
- **Business Logic Layer**: PushTo business processing classes
- **Data Access Layer**: Dapper ORM
- **Utility Layer**: Various helper utility classes

### 6.2 Technology Stack
- **Framework**: .NET Core 3.1
- **ORM**: Dapper 2.0.35
- **Logging**: NLog 4.7.2
- **API Documentation**: Swagger 5.5.1
- **Database**: SQL Server
- **HTTP Client**: HttpClient
- **JSON Processing**: Newtonsoft.Json

### 6.3 External System Integration
- **A8 Collaborative Office System**: Workflow management
- **File Service**: Attachment upload and management
- **Authentication Service**: SSO single sign-on

### 6.4 Core Components

#### 6.4.1 Controller Layer
- **PushController**: Main API controller

#### 6.4.2 Business Logic Layer
- **PushTo**: Core business processing class
- **ApiTools**: API utility class

#### 6.4.3 Utility Classes
- **TokenFetcher**: Token acquisition
- **HttpClientHelper**: HTTP request processing
- **UploadAttachment**: File upload
- **JsonReaderHelper**: JSON parsing
- **Logger**: Logging
- **AsyncHelper**: Asynchronous processing

## 7. Deployment and Runtime Requirements

### 7.1 System Requirements
- **Operating System**: Windows Server 2016+
- **Runtime**: .NET Core 3.1
- **Database**: SQL Server 2016+
- **Memory**: Minimum 4GB, Recommended 8GB
- **Storage**: Minimum 10GB available space

### 7.2 Configuration Requirements
- **Database connection string configuration**
- **A8 system API address configuration**
- **Authentication key configuration**
- **Log level configuration**

### 7.3 Network Requirements
- **Outbound HTTPS connections**: Access to A8 system APIs
- **Database connectivity**: SQL Server access
- **File upload bandwidth**: Recommended 10Mbps+

### 7.4 Security Configuration
- **HTTPS certificate configuration**
- **Database access permissions**
- **API access control**
- **Sensitive configuration encryption**

## 8. Monitoring and Maintenance

### 8.1 Log Monitoring
- **Application logs**: NLog recording
- **Error logs**: Exception tracking
- **Performance logs**: Response time monitoring
- **Business logs**: Operation auditing

### 8.2 Health Checks
- **API availability checks**
- **Database connection checks**
- **External service dependency checks**
- **File system checks**

### 8.3 Backup Strategy
- **Regular database backups**
- **Configuration file backups**
- **Log file archiving**
- **Disaster recovery planning**

## 9. Detailed System Configuration

### 9.1 Application Configuration

#### 9.1.1 Authentication Configuration
```json
{
  "AuthKey4SsoToken": "6862d39b4aea09643cf179648aa6ad7a73e9ad0f",
  "AuthKey4AccessToken": "3cc3bc9cfb846c9b3ff509ebc916fbbe1206aa66"
}
```

#### 9.1.2 API Endpoint Configuration
```json
{
  "BASE_URL": "https://wfapi.3311csci.com",
  "ApiUrl_UploadAttachment": "https://i.3311csci.com/seeyon/rest/attachment",
  "A8_Workflow_URL": "https://wfapi.3311csci.com/systems/a8/workflows",
  "A8_BPM_URL": "https://i.3311csci.com/seeyon/rest/bpm/process/start"
}
```

#### 9.1.3 Database Configuration
```json
{
  "ConnectionStrings": {
    "live_ilodata": "Data Source=cob-server-090;User ID=common;PassWord=***;Initial Catalog=live_ilodata;MultipleActiveResultSets=True;Connection Timeout=400",
    "live_isdata": "Data Source=cob-server-090;User ID=common;PassWord=***;Initial Catalog=live_isdata;MultipleActiveResultSets=True;Connection Timeout=400"
  }
}
```

### 9.2 Workflow Template Configuration

#### 9.2.1 A8 Workflow Template IDs
- **polh**: Policy workflow template
- **Business**: Business report workflow template
- **Claims**: Claims report workflow template
- **CoverNote**: Cover note workflow template
- **oinvh**: Invoice workflow template

#### 9.2.2 Form Field Mapping
Form field mapping relationships for each business type to ensure data is correctly pushed to corresponding form fields in the A8 system.

## 10. Error Handling and Exception Management

### 10.1 Exception Type Classification
- **Network Exceptions**: HTTP request failures, timeouts, etc.
- **Authentication Exceptions**: Token expiration, authentication failures, etc.
- **Business Exceptions**: Data validation failures, business rule violations, etc.
- **System Exceptions**: Database connection failures, file operation failures, etc.

### 10.2 Error Response Format
```json
{
  "Code": "00000",
  "Message": "Specific reason for operation failure",
  "Result": "Detailed error information or null"
}
```

### 10.3 Retry Mechanisms
- Automatic retry 3 times for network request failures
- Automatic retry after token refresh on expiration
- File upload failure retry mechanism

## 11. Detailed Business Process Description

### 11.1 Complete Document Push Process
1. **Receive Request**: API receives upload request
2. **Data Validation**: Validate request parameters and business data
3. **Database Query**: Query related data based on business type
4. **Duplicate Check**: Check if already pushed
5. **Token Acquisition**: Obtain A8 system access token
6. **File Upload**: Upload attachments to A8 system
7. **Workflow Trigger**: Create and start A8 workflow
8. **Status Update**: Update process status in database
9. **Response Return**: Return processing result

### 11.2 Status Management
- **Pending Push**: Data prepared, waiting for push
- **Pushing**: Currently executing push operation
- **Pushed**: Successfully pushed to A8 system
- **Push Failed**: Error occurred during push process

## 12. Performance Optimization Recommendations

### 12.1 Database Optimization
- Add appropriate indexes for query fields
- Use connection pooling for database connections
- Regular cleanup of historical data

### 12.2 File Processing Optimization
- Implement chunked file upload
- Add file compression functionality
- Implement resumable upload mechanism

### 12.3 Caching Strategy
- Token caching mechanism
- Configuration information caching
- Query result caching

## 13. Testing Strategy

### 13.1 Unit Testing
- Business logic unit tests
- Data access layer tests
- Utility class functionality tests

### 13.2 Integration Testing
- API endpoint integration tests
- Database integration tests
- External system integration tests

### 13.3 Performance Testing
- Load testing
- Stress testing
- File upload performance testing

## 14. Version Control and Release

### 14.1 Version Numbering Rules
Adopt Semantic Versioning:
- Major version: Incompatible API changes
- Minor version: Backward-compatible functionality additions
- Patch version: Backward-compatible bug fixes

### 14.2 Release Process
1. Code review
2. Automated testing
3. Pre-production environment verification
4. Production environment deployment
5. Post-release monitoring

---

*Document Version: 1.0*
*Last Updated: July 2025*
*Document Status: Official Release*
