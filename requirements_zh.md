# 保險業務文檔推送系統需求規格書

## 1. 項目概述

### 1.1 項目背景
保險業務文檔推送系統是一個基於ASP.NET Core 3.1開發的Web API服務，主要用於將保險業務相關文檔自動推送到A8協同辦公系統，實現業務流程的數字化管理。

### 1.2 項目目標
- 實現保險業務文檔的自動化推送
- 與A8協同辦公系統無縫集成
- 支持多種業務類型的文檔處理
- 提供可靠的文件上傳和工作流觸發機制

### 1.3 系統範圍
系統涵蓋保單管理、理賠處理、暫保單、各類發票處理等核心保險業務場景。

## 2. 功能需求

### 2.1 核心業務功能

#### 2.1.1 保單文檔推送 (Policy)
- **功能描述**：處理保單相關文檔的推送
- **數據來源**：polh表（保單主表）
- **觸發條件**：fconfirm in (2,3) and fctldel = ''
- **推送內容**：
  - 流程編號 (fctlid)
  - 保單號 (fpolno)
  - 批單號 (fendtno)
  - 項目名稱 (fpartr)
  - 合約金額 (fsum)
  - 保險期限 (fefffr - feffto)
  - 保單摘要 (fcontrt_t1)
  - 投保人 (finsd)
  - 附件文檔

#### 2.1.2 業務報表推送 (Business)
- **功能描述**：處理業務報表的推送
- **數據來源**：reportrecord表
- **觸發條件**：flowid = '' and status = '1'
- **推送內容**：
  - 報表名稱 (reportname)
  - 日期 (reportdate)
  - 流程編號 (ID)
  - 附件文檔

#### 2.1.3 理賠報表推送 (Claims)
- **功能描述**：處理理賠報表的推送
- **數據來源**：reportrecord表
- **觸發條件**：flowid = '' and status = '2'
- **推送內容**：
  - 報表名稱 (reportname)
  - 日期 (reportdate)
  - 流程編號 (ID)
  - 附件文檔

#### 2.1.4 暫保單推送 (CoverNote)
- **功能描述**：處理暫保單文檔的推送
- **數據來源**：cover_note表
- **觸發條件**：flowid = '' and fconfirm = '3'
- **推送內容**：
  - 暫保單號 (CoverNo)
  - 開始日期 (BeginDate)
  - 被保險人 (IssueName)
  - 附件文檔

#### 2.1.5 發票處理推送
支持多種發票類型的處理：

**a) 一般發票 (OinvhDRCR)**
- 數據來源：oinvh表
- 觸發條件：fstatus = '3'

**b) COIS發票 (OinvhDRCR_COIS)**
- 數據來源：oinvh表（live_isdata庫）
- 觸發條件：fposted = '1'

**c) 會計發票 (OinvhDRCR_Acc)**
- 數據來源：oginvh表
- 觸發條件：fposted = '1'

**d) 原始發票 (oriinvh)**
- 處理原始發票數據

**e) 支付發票 (opyinvh, opyrinvh)**
- 支持理賠支付相關發票處理

### 2.2 文件管理功能

#### 2.2.1 文件上傳
- 支持PDF格式文檔上傳
- 支持主文檔和額外文檔（RI再保險文檔）
- 自動生成文件名規則
- 文件大小和格式驗證

#### 2.2.2 附件管理
- 與A8系統的附件服務集成
- 支持多文件批量上傳
- 文件關聯到工作流實例

### 2.3 工作流集成

#### 2.3.1 A8系統集成
- SSO單點登錄認證
- 工作流自動觸發
- 流程狀態跟踪
- 錯誤處理和重試機制

#### 2.3.2 令牌管理
- SSO加密令牌獲取
- 系統訪問令牌管理
- REST API令牌處理
- 令牌自動刷新

## 3. 非功能需求

### 3.1 性能需求
- API響應時間：< 5秒
- 文件上傳支持：最大50MB
- 併發處理：支持100個併發請求
- 數據庫連接超時：400秒

### 3.2 可靠性需求
- 系統可用性：99.5%
- 數據一致性保證
- 異常處理和錯誤恢復
- 日誌記錄和監控

### 3.3 安全性需求
- API訪問控制
- 數據傳輸加密
- 敏感信息保護
- 審計日誌記錄

### 3.4 可維護性需求
- 模塊化設計
- 配置外部化
- 詳細的錯誤日誌
- API文檔完整

## 4. 數據結構說明

### 4.1 核心數據模型

#### 4.1.1 Policy（保單模型）
```csharp
public class Policy
{
    public string Fctlid { get; set; }      // 流程編號
    public string Fpolno { get; set; }      // 保單號
    public string Fendtno { get; set; }     // 批單號
    public string Fsum { get; set; }        // 保險金額
    public string Fefffr { get; set; }      // 保險起期
    public string Feffto { get; set; }      // 保險止期
    public string Fpartr { get; set; }      // 項目名稱
    public string Finsd { get; set; }       // 投保人
    public string Fcontrt_t1 { get; set; }  // 保單摘要
}
```

#### 4.1.2 CoverNote（暫保單模型）
```csharp
public class CoverNote
{
    public string Fctlid { get; set; }      // 流程編號
    public string CoverNo { get; set; }     // 暫保單號
    public string IssueName { get; set; }   // 被保險人
    public string BeginDate { get; set; }   // 開始日期
    public string IssueDate { get; set; }   // 簽發日期
}
```

#### 4.1.3 DrCr（發票模型）
```csharp
public class DrCr
{
    public string Fctlid { get; set; }      // 流程編號
    public string Finvno { get; set; }      // 發票號
    public string Fpolno { get; set; }      // 保單號
    public string Fclmno { get; set; }      // 理賠號
    public string Fbilldate { get; set; }   // 憑單日期
    public string Fcremark { get; set; }    // 摘要
    public string Fcinpname { get; set; }   // 經辦人
}
```

#### 4.1.4 Report（報表模型）
```csharp
public class Report
{
    public string ID { get; set; }          // 報表ID
    public string reportdate { get; set; }  // 報表日期
    public string reportname { get; set; }  // 報表名稱
    public string filename { get; set; }    // 文件名
}
```

### 4.2 數據傳輸對象

#### 4.2.1 UploadRequest（上傳請求）
```csharp
public class UploadRequest
{
    public string Fctlid { get; set; }           // 流程編號
    public string Polno { get; set; }            // 保單號
    public string UserId { get; set; }           // 用戶ID
    public string Postype { get; set; }          // 業務類型
    public string Fpayee { get; set; }           // 收款人
    public string Yccamount { get; set; }        // 港幣大寫
    public string Yamount { get; set; }          // 港幣金額
    public byte[] FileContent { get; set; }      // 主文檔內容
    public byte[] ExtraFileContent { get; set; } // 額外文檔內容
}
```

#### 4.2.2 JsonOutput（響應輸出）
```csharp
public class JsonOutput
{
    public string Code { get; set; }        // 響應碼
    public string Message { get; set; }     // 響應消息
    public string Result { get; set; }      // 響應結果
}
```

### 4.3 數據庫表結構

#### 4.3.1 主要業務表
- **polh**：保單主表
- **oinvh**：發票表
- **oginvh**：會計發票表
- **cover_note**：暫保單表
- **reportrecord**：報表記錄表

#### 4.3.2 數據庫連接
- **live_ilodata**：主業務數據庫
- **live_isdata**：COIS系統數據庫

## 5. API規格概述

### 5.1 API端點

#### 5.1.1 GET /api/Push
- **功能**：查詢流程狀態並更新
- **參數**：Id（用戶ID）
- **返回**：JsonOutput

#### 5.1.2 POST /api/Push
- **功能**：文檔上傳和推送
- **參數**：UploadRequest對象
- **返回**：JsonOutput

### 5.2 響應碼定義
- **10200**：成功
- **10500**：已上傳
- **00000**：操作失敗

### 5.3 業務類型 (Postype)
- **Policy**：保單
- **Business**：業務報表
- **Claims**：理賠報表
- **CoverNote**：暫保單
- **OinvhDRCR**：一般發票
- **OinvhDRCR_COIS**：COIS發票
- **OinvhDRCR_Acc**：會計發票
- **oriinvh**：原始發票
- **opyinvh**：支付發票
- **opyrinvh**：支付退款發票

## 6. 技術架構描述

### 6.1 系統架構
- **架構模式**：分層架構
- **API層**：ASP.NET Core Web API
- **業務邏輯層**：PushTo業務處理類
- **數據訪問層**：Dapper ORM
- **工具層**：各種輔助工具類

### 6.2 技術棧
- **框架**：.NET Core 3.1
- **ORM**：Dapper 2.0.35
- **日誌**：NLog 4.7.2
- **API文檔**：Swagger 5.5.1
- **數據庫**：SQL Server
- **HTTP客戶端**：HttpClient
- **JSON處理**：Newtonsoft.Json

### 6.3 外部系統集成
- **A8協同辦公系統**：工作流管理
- **文件服務**：附件上傳和管理
- **認證服務**：SSO單點登錄

### 6.4 核心組件

#### 6.4.1 控制器層
- **PushController**：主要API控制器

#### 6.4.2 業務邏輯層
- **PushTo**：核心業務處理類
- **ApiTools**：API工具類

#### 6.4.3 工具類
- **TokenFetcher**：令牌獲取
- **HttpClientHelper**：HTTP請求處理
- **UploadAttachment**：文件上傳
- **JsonReaderHelper**：JSON解析
- **Logger**：日誌記錄
- **AsyncHelper**：異步處理

## 7. 部署和運行要求

### 7.1 系統要求
- **操作系統**：Windows Server 2016+
- **運行時**：.NET Core 3.1
- **數據庫**：SQL Server 2016+
- **內存**：最低4GB，推薦8GB
- **存儲**：最低10GB可用空間

### 7.2 配置要求
- **數據庫連接字符串配置**
- **A8系統API地址配置**
- **認證密鑰配置**
- **日誌級別配置**

### 7.3 網絡要求
- **出站HTTPS連接**：訪問A8系統API
- **數據庫連接**：SQL Server訪問
- **文件上傳帶寬**：建議10Mbps+

### 7.4 安全配置
- **HTTPS證書配置**
- **數據庫訪問權限**
- **API訪問控制**
- **敏感配置加密**

## 8. 監控和維護

### 8.1 日誌監控
- **應用程序日誌**：NLog記錄
- **錯誤日誌**：異常跟踪
- **性能日誌**：響應時間監控
- **業務日誌**：操作審計

### 8.2 健康檢查
- **API可用性檢查**
- **數據庫連接檢查**
- **外部服務依賴檢查**
- **文件系統檢查**

### 8.3 備份策略
- **數據庫定期備份**
- **配置文件備份**
- **日誌文件歸檔**
- **災難恢復計劃**

## 9. 系統配置詳細說明

### 9.1 應用程序配置

#### 9.1.1 認證配置
```json
{
  "AuthKey4SsoToken": "6862d39b4aea09643cf179648aa6ad7a73e9ad0f",
  "AuthKey4AccessToken": "3cc3bc9cfb846c9b3ff509ebc916fbbe1206aa66"
}
```

#### 9.1.2 API端點配置
```json
{
  "BASE_URL": "https://wfapi.3311csci.com",
  "ApiUrl_UploadAttachment": "https://i.3311csci.com/seeyon/rest/attachment",
  "A8_Workflow_URL": "https://wfapi.3311csci.com/systems/a8/workflows",
  "A8_BPM_URL": "https://i.3311csci.com/seeyon/rest/bpm/process/start"
}
```

#### 9.1.3 數據庫配置
```json
{
  "ConnectionStrings": {
    "live_ilodata": "Data Source=cob-server-090;User ID=common;PassWord=***;Initial Catalog=live_ilodata;MultipleActiveResultSets=True;Connection Timeout=400",
    "live_isdata": "Data Source=cob-server-090;User ID=common;PassWord=***;Initial Catalog=live_isdata;MultipleActiveResultSets=True;Connection Timeout=400"
  }
}
```

### 9.2 工作流模板配置

#### 9.2.1 A8工作流模板ID
- **polh**: 保單工作流模板
- **Business**: 業務報表工作流模板
- **Claims**: 理賠報表工作流模板
- **CoverNote**: 暫保單工作流模板
- **oinvh**: 發票工作流模板

#### 9.2.2 表單字段映射
各業務類型的表單字段映射關係，確保數據正確推送到A8系統對應的表單字段。

## 10. 錯誤處理和異常管理

### 10.1 異常類型分類
- **網絡異常**: HTTP請求失敗、超時等
- **認證異常**: 令牌過期、認證失敗等
- **業務異常**: 數據驗證失敗、業務規則違反等
- **系統異常**: 數據庫連接失敗、文件操作失敗等

### 10.2 錯誤響應格式
```json
{
  "Code": "00000",
  "Message": "操作失敗的具體原因",
  "Result": "詳細錯誤信息或空值"
}
```

### 10.3 重試機制
- 網絡請求失敗自動重試3次
- 令牌過期自動刷新後重試
- 文件上傳失敗重試機制

## 11. 業務流程詳細說明

### 11.1 文檔推送完整流程
1. **接收請求**: API接收上傳請求
2. **數據驗證**: 驗證請求參數和業務數據
3. **數據庫查詢**: 根據業務類型查詢相關數據
4. **重複檢查**: 檢查是否已經推送過
5. **令牌獲取**: 獲取A8系統訪問令牌
6. **文件上傳**: 上傳附件到A8系統
7. **工作流觸發**: 創建並啟動A8工作流
8. **狀態更新**: 更新數據庫中的流程狀態
9. **響應返回**: 返回處理結果

### 11.2 狀態管理
- **待推送**: 數據已準備，等待推送
- **推送中**: 正在執行推送操作
- **已推送**: 成功推送到A8系統
- **推送失敗**: 推送過程中發生錯誤

## 12. 性能優化建議

### 12.1 數據庫優化
- 為查詢字段添加適當索引
- 使用連接池管理數據庫連接
- 定期清理歷史數據

### 12.2 文件處理優化
- 實現文件分塊上傳
- 添加文件壓縮功能
- 實現斷點續傳機制

### 12.3 緩存策略
- 令牌緩存機制
- 配置信息緩存
- 查詢結果緩存

## 13. 測試策略

### 13.1 單元測試
- 業務邏輯單元測試
- 數據訪問層測試
- 工具類功能測試

### 13.2 集成測試
- API端點集成測試
- 數據庫集成測試
- 外部系統集成測試

### 13.3 性能測試
- 負載測試
- 壓力測試
- 文件上傳性能測試

## 14. 版本控制和發布

### 14.1 版本號規則
採用語義化版本控制（Semantic Versioning）：
- 主版本號：不兼容的API修改
- 次版本號：向下兼容的功能性新增
- 修訂號：向下兼容的問題修正

### 14.2 發布流程
1. 代碼審查
2. 自動化測試
3. 預發布環境驗證
4. 生產環境部署
5. 發布後監控

---

*本文檔版本：1.0*
*最後更新：2025年7月*
*文檔狀態：正式版*
